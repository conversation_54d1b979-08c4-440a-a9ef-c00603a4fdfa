"""
调试标题提取缺失和表格格式问题
"""

from pdf_document_parser import PdfDocumentParser
import fitz

def debug_heading_table_issues():
    """调试标题提取和表格格式问题"""
    
    pdf_file_path = "D:\\工作文档\\20210826头条放心借\\智选平台_基线文档_v0.0.1.pdf"
    
    print("调试标题提取和表格格式问题")
    print("=" * 60)
    
    try:
        parser = PdfDocumentParser(pdf_file_path)
        
        # 检查标题提取问题
        print("1. 检查标题提取问题:")
        
        # 获取当前提取的标题
        current_headings = parser.get_all_headings()
        print(f"   当前提取的标题数量: {len(current_headings)}")
        
        # 手动检查几个页面的加粗文本
        doc = fitz.open(pdf_file_path)
        
        print("\n   检查前5页的加粗文本:")
        for page_num in range(min(5, len(doc))):
            page = doc[page_num]
            text_dict = page.get_text("dict", flags=fitz.TEXTFLAGS_DICT)
            
            print(f"\n   页面 {page_num + 1}:")
            bold_texts = []
            
            for block in text_dict["blocks"]:
                if block.get("type") == 0:  # 文本块
                    for line in block.get("lines", []):
                        for span in line.get("spans", []):
                            if span.get("flags", 0) & 16:  # 加粗标志
                                text = span.get("text", "").strip()
                                if text and len(text) < 100:  # 短文本可能是标题
                                    bold_texts.append(text)
            
            # 去重并显示
            unique_bold_texts = list(dict.fromkeys(bold_texts))
            for i, text in enumerate(unique_bold_texts[:10]):  # 只显示前10个
                print(f"     {i+1}. {text}")
        
        doc.close()
        
        # 检查表格格式问题
        print("\n2. 检查表格格式问题:")
        
        # 获取markdown内容
        markdown_content = parser.get_markdown_content()
        
        # 检查是否包含markdown表格
        lines = markdown_content.split('\n')
        table_lines = []
        
        for i, line in enumerate(lines):
            if '|' in line and ('---' in line or line.count('|') >= 3):
                table_lines.append((i, line))
        
        print(f"   在Markdown中找到 {len(table_lines)} 行可能的表格内容")
        
        if table_lines:
            print("\n   前5个表格行示例:")
            for i, (line_num, line) in enumerate(table_lines[:5]):
                print(f"     行{line_num}: {line[:80]}...")
        
        # 检查原始表格数据
        content_blocks = parser._parse_document()
        table_blocks = [block for block in content_blocks if block['type'] == 'table']
        
        print(f"\n   解析出的表格块数量: {len(table_blocks)}")
        
        if table_blocks:
            print("\n   前3个表格的数据结构:")
            for i, table_block in enumerate(table_blocks[:3]):
                data = table_block.get('data', [])
                rows = len(data)
                cols = len(data[0]) if data else 0
                print(f"     表格{i+1}: {rows}行 x {cols}列")
                
                # 显示表格的前几行
                if data:
                    print("       前3行数据:")
                    for j, row in enumerate(data[:3]):
                        row_text = " | ".join([str(cell)[:20] + "..." if len(str(cell)) > 20 else str(cell) for cell in row])
                        print(f"         行{j+1}: {row_text}")
        
        # 测试表格格式化方法
        print("\n3. 测试表格格式化:")
        
        if table_blocks:
            test_table = table_blocks[0]
            test_data = test_table.get('data', [])
            
            if test_data:
                # 测试当前的markdown格式化
                formatted_table = parser._format_table_as_markdown(test_data)
                print("\n   当前格式化结果:")
                print(formatted_table[:300] + "..." if len(formatted_table) > 300 else formatted_table)
        
        print("\n4. 分析问题:")
        
        # 分析标题提取问题
        print("   标题提取问题分析:")
        print("   - 当前标题检测可能过于严格")
        print("   - 需要放宽对加粗文本的标题识别")
        print("   - 可能需要考虑更多的标题模式")
        
        # 分析表格格式问题
        print("\n   表格格式问题分析:")
        print("   - 需要检查表格数据是否正确提取")
        print("   - 需要确保markdown表格格式正确")
        print("   - 可能需要改进表格格式化方法")
        
    except Exception as e:
        print(f"调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_heading_table_issues()
