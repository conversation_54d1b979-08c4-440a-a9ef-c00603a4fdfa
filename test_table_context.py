"""
测试表格上下文，寻找更好的表头
"""

from pdf_document_parser import PdfDocumentParser

def test_table_context():
    """测试表格上下文"""
    
    pdf_file_path = "D:\\工作文档\\20210826头条放心借\\智选平台_基线文档_v0.0.1.pdf"
    
    print("测试表格上下文")
    print("=" * 60)
    
    try:
        parser = PdfDocumentParser(pdf_file_path)
        
        # 获取markdown内容
        markdown_content = parser.get_markdown_content()
        lines = markdown_content.split('\n')
        
        # 找到表格位置
        table_start = -1
        for i, line in enumerate(lines):
            if line.strip() == "| 列1 | 列2 | 列3 |":
                table_start = i
                break
        
        if table_start != -1:
            print(f"找到表格开始位置: 行 {table_start + 1}")
            
            # 显示表格前后的上下文
            context_start = max(0, table_start - 10)
            context_end = min(len(lines), table_start + 15)
            
            print("\n表格前后的上下文:")
            for i in range(context_start, context_end):
                prefix = ">>>" if i == table_start else "   "
                print(f"{prefix} 行{i+1:4d}: {lines[i]}")
            
            # 分析表格前的内容，寻找可能的表头信息
            print("\n分析表格前的内容:")
            for i in range(table_start - 1, max(0, table_start - 10), -1):
                line = lines[i].strip()
                if line:
                    print(f"   行{i+1}: {line}")
                    
                    # 检查是否包含表头相关信息
                    if any(keyword in line for keyword in ['错误码', '状态码', '返回码', '结果', '类型', '说明']):
                        print(f"     ↑ 可能包含表头信息")
                    
                    if line.startswith('#'):
                        print(f"     ↑ 这是标题，可能描述了表格内容")
                        break
        
        # 分析表格数据，推断可能的列含义
        content_blocks = parser._parse_document()
        table_blocks = [block for block in content_blocks if block['type'] == 'table']
        
        if table_blocks:
            table_data = table_blocks[0].get('data', [])
            
            print(f"\n分析表格数据模式:")
            print(f"表格有 {len(table_data)} 行，{len(table_data[0]) if table_data else 0} 列")
            
            # 分析每列的数据特征
            if table_data:
                for col_idx in range(len(table_data[0])):
                    col_values = [row[col_idx] for row in table_data if col_idx < len(row)]
                    
                    print(f"\n第{col_idx + 1}列分析:")
                    print(f"  前5个值: {col_values[:5]}")
                    
                    # 分析数据模式
                    if all('_' in str(val) and any(c.isdigit() for c in str(val)) for val in col_values[:5]):
                        print(f"  → 可能是错误码/状态码")
                    elif all(len(str(val)) > 10 for val in col_values[:5]):
                        print(f"  → 可能是说明/描述")
                    elif all(len(str(val)) < 10 for val in col_values[:5]):
                        print(f"  → 可能是类型/分类")
        
        print("\n建议的表头改进:")
        print("基于数据分析，建议的表头可能是:")
        print("| 错误类型 | 错误码 | 错误说明 |")
        print("或者:")
        print("| 业务场景 | 状态码 | 状态描述 |")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_table_context()
