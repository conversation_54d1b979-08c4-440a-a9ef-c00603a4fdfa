"""
PDF文档解析器
专门用于解析PDF文档，提供原始内容、markdown格式转换和标题提取功能
使用PyMuPDF进行PDF文档解析，支持图片提取
支持本地文件路径和HTTP文档链接
"""

import re
import os
import tempfile
import requests
import fitz  # PyMuPDF
import base64
from urllib.parse import urlparse
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path


class PdfDocumentParser:
    """PDF文档解析器，支持本地文件和HTTP链接"""

    def __init__(self, file_path: str):
        """
        初始化PDF文档解析器

        Args:
            file_path: PDF文档路径（本地路径或HTTP URL）
        """
        self.original_path = file_path
        self.file_path = file_path
        self.document = None
        self._parsed_content = None
        self._headings = None
        self._temp_file = None
        self._is_url = False

        # 检查是否为HTTP URL
        if self._is_http_url(file_path):
            self._is_url = True
            self.file_path = self._download_file(file_path)
        else:
            # 验证本地文件存在性
            if not Path(file_path).exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

        # 验证文件格式
        file_ext = Path(self.file_path).suffix.lower()
        if file_ext not in ['.pdf']:
            raise ValueError(f"不支持的文件格式: {file_ext}")

    def _is_http_url(self, path: str) -> bool:
        """检查路径是否为HTTP URL"""
        try:
            result = urlparse(path)
            return result.scheme in ('http', 'https')
        except Exception:
            return False

    def _download_file(self, url: str) -> str:
        """
        从HTTP URL下载文件到临时目录

        Args:
            url: 文件的HTTP URL

        Returns:
            str: 临时文件路径
        """
        try:
            # 发送HTTP请求下载文件
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # 从URL或Content-Disposition头获取文件名
            filename = self._get_filename_from_url(url, response)

            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, filename)

            # 写入文件内容
            with open(temp_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            self._temp_file = temp_path
            return temp_path

        except requests.RequestException as e:
            raise Exception(f"下载文件失败: {str(e)}")
        except Exception as e:
            raise Exception(f"处理下载文件时出错: {str(e)}")

    def _get_filename_from_url(self, url: str, response: requests.Response) -> str:
        """从URL或响应头获取文件名"""
        # 尝试从Content-Disposition头获取文件名
        content_disposition = response.headers.get('content-disposition')
        if content_disposition:
            import re
            filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
            if filename_match:
                filename = filename_match.group(1).strip('"\'')
                return filename

        # 从URL路径获取文件名
        path = urlparse(url).path
        filename = os.path.basename(path)

        # 如果文件名为空或没有扩展名，使用默认文件名
        if not filename or '.' not in filename:
            filename = f"downloaded_document_{hash(url)}.pdf"

        return filename

    def __del__(self):
        """析构函数，清理资源"""
        self._cleanup_temp_file()
        if self.document:
            try:
                self.document.close()
            except Exception:
                pass

    def _cleanup_temp_file(self):
        """清理临时文件"""
        if hasattr(self, '_temp_file') and self._temp_file and os.path.exists(self._temp_file):
            try:
                os.remove(self._temp_file)
            except Exception:
                pass  # 忽略删除失败的情况
    
    def _load_document(self) -> fitz.Document:
        """加载PDF文档"""
        if self.document is None:
            try:
                self.document = fitz.open(self.file_path)
            except Exception as e:
                raise Exception(f"无法打开PDF文档: {str(e)}")
        return self.document
    
    def _parse_document(self) -> List[Dict[str, Any]]:
        """
        解析文档内容，按原文档顺序返回所有内容块
        
        Returns:
            List[Dict]: 内容块列表
        """
        if self._parsed_content is not None:
            return self._parsed_content
        
        doc = self._load_document()
        content_blocks = []
        
        # 遍历所有页面
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # 解析页面内容
            page_blocks = self._parse_page(page, page_num)
            content_blocks.extend(page_blocks)
        
        # 按页面和位置排序内容块
        content_blocks = self._sort_blocks_by_position(content_blocks)
        
        self._parsed_content = content_blocks
        return content_blocks
    
    def _sort_blocks_by_position(self, blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        按页面和位置排序内容块，确保严格按照文档原始顺序

        Args:
            blocks: 内容块列表

        Returns:
            List[Dict]: 排序后的内容块列表
        """
        def get_sort_key(block):
            page_num = block.get('page_num', 0)
            y_pos = block.get('position_y', 0)
            x_pos = block.get('position_x', 0)

            # 使用精确的排序：页面 -> Y坐标 -> X坐标
            # 不进行任何舍入，保持原始精度
            return (page_num, y_pos, x_pos)

        # 严格按照位置排序，不做任何调整
        return sorted(blocks, key=get_sort_key)

    def _parse_page(self, page: fitz.Page, page_num: int) -> List[Dict[str, Any]]:
        """
        解析单个页面，先识别标题，再处理表格，确保标题不被放入表格

        Args:
            page: 页面对象
            page_num: 页面编号

        Returns:
            List[Dict]: 页面内容块列表
        """
        all_blocks = []

        # 第一步：先处理所有文本块，识别标题和段落
        text_dict = page.get_text("dict", flags=fitz.TEXTFLAGS_DICT)
        text_blocks = []
        heading_areas = []  # 记录标题区域，避免表格检测时包含标题

        for block in text_dict["blocks"]:
            if block.get("type") == 0:  # 文本块
                text_block = self._parse_text_block_new(block, page_num)
                if text_block:
                    text_blocks.append(text_block)
                    # 如果是标题，记录其区域
                    if text_block['type'] == 'heading':
                        bbox = text_block['bbox']
                        # 扩展标题区域，确保表格检测时避开
                        expanded_bbox = [
                            bbox[0] - 10,  # 左边界向左扩展
                            bbox[1] - 5,   # 上边界向上扩展
                            bbox[2] + 10,  # 右边界向右扩展
                            bbox[3] + 5    # 下边界向下扩展
                        ]
                        heading_areas.append(expanded_bbox)
            elif block.get("type") == 1:  # 图片块
                image_block = self._parse_image_block(block, page, page_num)
                if image_block:
                    all_blocks.append(image_block)

        # 第二步：处理表格，但要避开标题区域
        tables = page.find_tables()
        table_blocks = []

        for table_idx, table in enumerate(tables.tables):
            if hasattr(table, 'bbox'):
                table_bbox = table.bbox

                # 检查表格是否与标题区域重叠
                overlaps_with_heading = False
                for heading_bbox in heading_areas:
                    if self._bboxes_overlap(table_bbox, heading_bbox):
                        overlaps_with_heading = True
                        break

                # 只处理不与标题重叠的表格
                if not overlaps_with_heading:
                    table_block = self._parse_table_new(table, page_num, table_idx)
                    if table_block:
                        table_blocks.append(table_block)

        # 第三步：合并所有块并按位置排序
        all_blocks.extend(text_blocks)
        all_blocks.extend(table_blocks)

        # 按位置排序
        all_blocks.sort(key=lambda x: (x['position_y'], x['position_x']))

        return all_blocks

    def _bboxes_overlap(self, bbox1: List[float], bbox2: List[float]) -> bool:
        """
        检查两个边界框是否重叠

        Args:
            bbox1: 第一个边界框 [x0, y0, x1, y1]
            bbox2: 第二个边界框 [x0, y0, x1, y1]

        Returns:
            bool: 是否重叠
        """
        return not (bbox1[2] <= bbox2[0] or  # bbox1在bbox2左边
                   bbox1[0] >= bbox2[2] or   # bbox1在bbox2右边
                   bbox1[3] <= bbox2[1] or   # bbox1在bbox2上边
                   bbox1[1] >= bbox2[3])     # bbox1在bbox2下边

    def _parse_text_block_new(self, block: Dict, page_num: int) -> Optional[Dict[str, Any]]:
        """
        新的文本块解析方法，不依赖表格区域检测

        Args:
            block: 文本块
            page_num: 页面编号

        Returns:
            Dict: 文本块信息
        """
        if 'lines' not in block:
            return None

        lines = block['lines']
        if not lines:
            return None

        # 提取文本内容和字体信息
        text_parts = []
        font_sizes = []
        font_flags = []

        for line in lines:
            if 'spans' not in line:
                continue

            for span in line['spans']:
                if 'text' in span and span['text'].strip():
                    text_parts.append(span['text'])
                    if 'size' in span:
                        font_sizes.append(span['size'])
                    if 'flags' in span:
                        font_flags.append(span['flags'])

        if not text_parts:
            return None

        text = "\n".join(text_parts)
        avg_font_size = sum(font_sizes) / len(font_sizes) if font_sizes else 12

        # 获取边界框信息
        bbox = block.get("bbox", [0, 0, 0, 0])

        # 使用新的标题检测逻辑
        heading_level = self._detect_heading_level_new(text, avg_font_size, font_flags)
        original_number = ""
        clean_text = text

        # 如果是标题，提取原始编号
        if heading_level > 0:
            original_number, clean_text = self._extract_heading_number(text)

        return {
            'type': 'heading' if heading_level > 0 else 'paragraph',
            'text': text,
            'clean_text': clean_text,
            'original_number': original_number,
            'level': heading_level if heading_level > 0 else None,
            'font_size': avg_font_size,
            'page_num': page_num,
            'bbox': bbox,
            'position_y': bbox[1] if len(bbox) >= 4 else 0,
            'position_x': bbox[0] if len(bbox) >= 4 else 0
        }

    def _detect_heading_level_new(self, text: str, font_size: float, font_flags: List = None) -> int:
        """
        改进的标题检测方法，能够识别更多加粗标题

        Args:
            text: 文本内容
            font_size: 字体大小
            font_flags: 字体标志列表

        Returns:
            int: 标题级别，0表示不是标题
        """
        # 如果文本为空，不是标题
        if not text.strip():
            return 0

        # 清理文本
        clean_text = text.strip()

        # 如果文本过长，不太可能是标题
        if len(clean_text) > 150:  # 放宽长度限制
            return 0

        # 检查是否包含表格特征
        has_table_chars = '|' in clean_text or '\t' in clean_text
        if has_table_chars:
            return 0

        # 严格的表格表头词汇检查（保持不变）
        table_header_words = [
            '参数名', '类型', '是否必须', '说明', '备注', '字段名', '字段类型', '必填', '描述',
            '须', '是', '否', '必', '明', '注', '请求地址', '请求方式', '数据类型', '性能要求',
            '参数', '字段', '地址', '方式', '数据', '请求', '响应', 'POST', 'GET', 'JSON'
        ]
        if clean_text in table_header_words:
            return 0

        # 检查是否为纯数字或简单符号
        if re.match(r'^[\d\.\s]+$', clean_text) or re.match(r'^[^\w\u4e00-\u9fff]+$', clean_text):
            return 0

        # 检查是否包含明确的标题编号模式
        has_clear_heading_number = bool(
            re.match(r'^(\d+\.)+\d*\s+[\u4e00-\u9fff\w]', clean_text) or  # 1.2.3 中文/英文
            re.match(r'^第[一二三四五六七八九十百千万亿]+[章节条款项]\s*[\u4e00-\u9fff\w]', clean_text) or  # 第一章
            re.match(r'^[一二三四五六七八九十]、\s*[\u4e00-\u9fff\w]', clean_text) or  # 一、
            re.match(r'^[IVXLCDMivxlcdm]+\.\s+[\u4e00-\u9fff\w]', clean_text) or  # I.
            re.match(r'^\d+\s+[\u4e00-\u9fff\w]', clean_text)  # 1 标题
        )

        # 检查字体特征
        is_large_font = font_size > 11  # 降低字体大小阈值
        is_bold = False
        if font_flags:
            is_bold = any(flag & 16 for flag in font_flags)

        # 检查是否为短文本且包含中文或英文字符
        is_short_text = len(clean_text) < 80 and clean_text.count('\n') < 3  # 放宽限制
        has_meaningful_content = bool(re.search(r'[\u4e00-\u9fff\w]', clean_text))

        # 检查是否为常见的标题关键词
        title_keywords = ['通信协议', '报文结构', '报⽂结构', '接口', '说明', '参数', '请求', '响应',
                         '示例', '场景', '规范', '格式', '方法', '流程', '步骤', '加密', '解密',
                         '签名', '验证', '配置', '设置']
        has_title_keywords = any(keyword in clean_text for keyword in title_keywords)

        # 放宽的标题判断逻辑
        if has_clear_heading_number and is_short_text and has_meaningful_content:
            # 有明确编号的标题
            if is_large_font and is_bold:
                return 1  # 一级标题
            elif is_large_font or is_bold:
                return 2  # 二级标题
            else:
                return 3  # 三级标题
        elif is_bold and is_short_text and has_meaningful_content:
            # 加粗的短文本，可能是标题
            if has_title_keywords or is_large_font:
                return 3  # 三级标题
            elif len(clean_text) < 30:  # 很短的加粗文本
                return 3  # 三级标题
        elif is_large_font and is_short_text and has_meaningful_content and has_title_keywords:
            # 大字体且包含标题关键词
            return 3  # 三级标题

        return 0  # 不是标题

    def _parse_table_new(self, table, page_num: int, table_idx: int) -> Optional[Dict[str, Any]]:
        """
        改进的表格解析方法，清理空列并优化表格数据

        Args:
            table: PyMuPDF表格对象
            page_num: 页面编号
            table_idx: 表格索引

        Returns:
            Dict: 表格块信息
        """
        try:
            # 提取表格数据
            table_data = table.extract()

            if not table_data:
                return None

            # 处理多行单元格，将换行符转换为<br>标签
            processed_data = []
            for row in table_data:
                processed_row = []
                for cell in row:
                    if cell is None:
                        processed_row.append("")
                    else:
                        # 将各种换行符转换为<br>标签
                        cell_str = str(cell).strip()
                        cell_str = cell_str.replace('\r\n', '<br>')
                        cell_str = cell_str.replace('\n', '<br>')
                        cell_str = cell_str.replace('\r', '<br>')
                        processed_row.append(cell_str)
                processed_data.append(processed_row)

            # 清理空列
            cleaned_data = self._clean_empty_columns(processed_data)

            # 清理空行
            cleaned_data = self._clean_empty_rows(cleaned_data)

            if not cleaned_data or not cleaned_data[0]:
                return None

            # 获取表格的边界框
            bbox = table.bbox if hasattr(table, 'bbox') else [0, 0, 0, 0]

            # 计算位置信息
            position_y = bbox[1] if len(bbox) >= 4 else 0
            position_x = bbox[0] if len(bbox) >= 4 else 0

            return {
                'type': 'table',
                'data': cleaned_data,
                'page_num': page_num,
                'table_idx': table_idx,
                'bbox': bbox,
                'position_y': position_y,
                'position_x': position_x,
                'source': 'pymupdf_native_cleaned',
                'table_height': bbox[3] - bbox[1] if len(bbox) >= 4 else 0,
                'table_width': bbox[2] - bbox[0] if len(bbox) >= 4 else 0
            }
        except Exception as e:
            return None

    def _clean_empty_columns(self, table_data: List[List[str]]) -> List[List[str]]:
        """
        清理表格中的空列

        Args:
            table_data: 原始表格数据

        Returns:
            List[List[str]]: 清理后的表格数据
        """
        if not table_data or not table_data[0]:
            return table_data

        # 找出所有空列的索引
        num_cols = len(table_data[0])
        empty_columns = []

        for col_idx in range(num_cols):
            is_empty = True
            for row in table_data:
                if col_idx < len(row) and row[col_idx].strip():
                    is_empty = False
                    break
            if is_empty:
                empty_columns.append(col_idx)

        # 如果没有空列，直接返回
        if not empty_columns:
            return table_data

        # 移除空列
        cleaned_data = []
        for row in table_data:
            cleaned_row = []
            for col_idx, cell in enumerate(row):
                if col_idx not in empty_columns:
                    cleaned_row.append(cell)
            cleaned_data.append(cleaned_row)

        return cleaned_data

    def _clean_empty_rows(self, table_data: List[List[str]]) -> List[List[str]]:
        """
        清理表格中的空行

        Args:
            table_data: 原始表格数据

        Returns:
            List[List[str]]: 清理后的表格数据
        """
        if not table_data:
            return table_data

        cleaned_data = []
        for row in table_data:
            # 检查行是否为空（所有单元格都为空或只包含空白字符）
            is_empty_row = all(not cell.strip() for cell in row)
            if not is_empty_row:
                cleaned_data.append(row)

        return cleaned_data

    def _parse_table(self, table, page_num: int, table_idx: int) -> Optional[Dict[str, Any]]:
        """
        解析表格，过滤掉标题行并确保正确的位置信息

        Args:
            table: PyMuPDF表格对象
            page_num: 页面编号
            table_idx: 表格索引

        Returns:
            Dict: 表格块信息
        """
        try:
            # 提取表格数据
            table_data = table.extract()

            if not table_data:
                return None

            # 过滤掉可能的标题行，只保留纯表格数据
            filtered_data = []
            for row_idx, row in enumerate(table_data):
                # 检查这一行是否看起来像标题
                row_text = ' '.join(str(cell) for cell in row if cell).strip()

                # 如果这一行看起来像标题，跳过它
                if self._is_table_row_heading(row_text):
                    continue

                # 处理多行单元格，将换行符转换为<br>标签
                processed_row = []
                for cell in row:
                    if cell is None:
                        processed_row.append("")
                    else:
                        # 将各种换行符转换为<br>标签
                        cell_str = str(cell)
                        cell_str = cell_str.replace('\r\n', '<br>')
                        cell_str = cell_str.replace('\n', '<br>')
                        cell_str = cell_str.replace('\r', '<br>')
                        processed_row.append(cell_str)
                filtered_data.append(processed_row)

            # 如果过滤后没有数据，返回None
            if not filtered_data:
                return None

            # 获取表格的边界框
            bbox = table.bbox if hasattr(table, 'bbox') else [0, 0, 0, 0]

            # 计算更精确的位置信息
            position_y = bbox[1] if len(bbox) >= 4 else 0
            position_x = bbox[0] if len(bbox) >= 4 else 0

            return {
                'type': 'table',
                'data': filtered_data,
                'page_num': page_num,
                'table_idx': table_idx,
                'bbox': bbox,
                'position_y': position_y,
                'position_x': position_x,
                'source': 'pymupdf_native',
                'table_height': bbox[3] - bbox[1] if len(bbox) >= 4 else 0,
                'table_width': bbox[2] - bbox[0] if len(bbox) >= 4 else 0
            }
        except Exception as e:
            return None

    def _is_table_row_heading(self, row_text: str) -> bool:
        """
        检查表格行是否为标题行

        Args:
            row_text: 行文本内容

        Returns:
            bool: 是否为标题行
        """
        if not row_text or len(row_text.strip()) == 0:
            return False

        # 检查是否包含标题编号模式
        heading_patterns = [
            r'^(\d+\.)+\d*\s+\S',  # 数字编号格式
            r'^第[一二三四五六七八九十百千万亿]+[章节条款项]',  # 中文章节格式
            r'^[一二三四五六七八九十]、',  # 中文顺序编号
            r'^[IVXLCDMivxlcdm]+\.\s+\S',  # 罗马数字编号
            r'^\d+\s+[^\d\s]',  # 简单数字编号
        ]

        for pattern in heading_patterns:
            if re.match(pattern, row_text):
                return True

        # 检查是否为短文本且不包含表格特征
        is_short = len(row_text) < 50
        has_table_chars = '|' in row_text or '\t' in row_text

        # 如果是短文本且不包含表格特征，可能是标题
        if is_short and not has_table_chars:
            # 进一步检查是否包含常见的标题关键词
            title_keywords = ['说明', '参数', '字段', '接口', '方法', '规范', '格式']
            for keyword in title_keywords:
                if keyword in row_text:
                    return True

        return False

    def _is_block_in_table_area(self, block: Dict, table_areas: List[Dict]) -> bool:
        """
        检查文本块是否在表格区域内

        Args:
            block: 文本块
            table_areas: 表格区域列表

        Returns:
            bool: 是否在表格区域内
        """
        block_bbox = block.get("bbox", [0, 0, 0, 0])

        for table_area in table_areas:
            table_bbox = table_area['bbox']

            # 检查是否有重叠
            if (block_bbox[0] < table_bbox[2] and block_bbox[2] > table_bbox[0] and
                block_bbox[1] < table_bbox[3] and block_bbox[3] > table_bbox[1]):
                return True

        return False

    def _parse_text_block(self, block: Dict, page_num: int, table_areas: List[Dict] = None) -> Optional[Dict[str, Any]]:
        """
        解析文本块，增强标题检测逻辑

        Args:
            block: 文本块
            page_num: 页面编号
            table_areas: 表格区域列表，用于避免将表格内容误识别为标题

        Returns:
            Dict: 文本块信息
        """
        if 'lines' not in block:
            return None

        lines = block['lines']
        if not lines:
            return None

        # 提取文本内容和字体信息
        text_parts = []
        font_sizes = []
        font_flags = []

        for line in lines:
            if 'spans' not in line:
                continue

            for span in line['spans']:
                if 'text' in span and span['text'].strip():
                    text_parts.append(span['text'])
                    if 'size' in span:
                        font_sizes.append(span['size'])
                    if 'flags' in span:
                        font_flags.append(span['flags'])

        if not text_parts:
            return None

        text = "\n".join(text_parts)
        avg_font_size = sum(font_sizes) / len(font_sizes) if font_sizes else 12

        # 获取边界框信息
        bbox = block.get("bbox", [0, 0, 0, 0])

        # 检查是否靠近表格区域（可能是表格标题）
        is_near_table = self._is_near_table_area(bbox, table_areas or [])

        # 检查是否在表格区域内（表格表头）
        is_in_table = self._is_block_in_table_area(block, table_areas or [])

        # 检测是否为标题（考虑表格上下文）
        heading_level = self._detect_heading_level(text, avg_font_size, lines, font_flags, is_near_table, is_in_table)
        original_number = ""
        clean_text = text

        # 如果是标题，提取原始编号
        if heading_level > 0:
            original_number, clean_text = self._extract_heading_number(text)

        return {
            'type': 'heading' if heading_level > 0 else 'paragraph',
            'text': text,
            'clean_text': clean_text,
            'original_number': original_number,
            'level': heading_level if heading_level > 0 else None,
            'font_size': avg_font_size,
            'page_num': page_num,
            'bbox': bbox,
            'position_y': bbox[1] if len(bbox) >= 4 else 0,  # 用于排序的Y坐标
            'position_x': bbox[0] if len(bbox) >= 4 else 0,  # 用于排序的X坐标
            'is_near_table': is_near_table,
            'is_in_table': is_in_table
        }

    def _is_near_table_area(self, text_bbox: List[float], table_areas: List[Dict]) -> bool:
        """
        检查文本块是否靠近表格区域

        Args:
            text_bbox: 文本块边界框
            table_areas: 表格区域列表

        Returns:
            bool: 是否靠近表格区域
        """
        if not table_areas:
            return False

        # 定义"靠近"的距离阈值
        proximity_threshold = 20

        for table_area in table_areas:
            table_bbox = table_area.get('original_bbox', table_area.get('bbox', [0, 0, 0, 0]))

            # 检查文本块是否在表格上方或下方的阈值范围内
            vertical_distance = min(
                abs(text_bbox[3] - table_bbox[1]),  # 文本底部到表格顶部的距离
                abs(text_bbox[1] - table_bbox[3])   # 文本顶部到表格底部的距离
            )

            # 检查水平重叠
            horizontal_overlap = (
                text_bbox[0] < table_bbox[2] and text_bbox[2] > table_bbox[0]
            )

            if vertical_distance <= proximity_threshold and horizontal_overlap:
                return True

        return False

    def _parse_image_block(self, block: Dict, page: fitz.Page, page_num: int) -> Optional[Dict[str, Any]]:
        """
        解析图片块

        Args:
            block: 图片块
            page: 页面对象
            page_num: 页面编号

        Returns:
            Dict: 图片块信息
        """
        try:
            # 获取图片信息
            xref = block.get("xref", 0)
            if xref <= 0:
                return None

            bbox = block.get("bbox", [0, 0, 0, 0])
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]

            # 提取图片
            pix = None
            try:
                pix = page.get_pixmap(matrix=fitz.Matrix(2, 2), clip=bbox)
            except Exception:
                return None

            if not pix:
                return None

            # 转换为base64编码
            img_data = pix.tobytes("png")
            b64_data = base64.b64encode(img_data).decode('utf-8')

            return {
                'type': 'image',
                'width': width,
                'height': height,
                'page_num': page_num,
                'bbox': bbox,
                'position_y': bbox[1] if len(bbox) >= 4 else 0,
                'base64_data': b64_data,
                'format': 'png'
            }
        except Exception as e:
            return None

    def _detect_heading_level(self, text: str, font_size: float, lines: List, font_flags: List = None, is_near_table: bool = False, is_in_table: bool = False) -> int:
        """
        检测文本是否为标题，并返回标题级别

        Args:
            text: 文本内容
            font_size: 字体大小
            lines: 行信息
            font_flags: 字体标志列表
            is_near_table: 是否靠近表格区域
            is_in_table: 是否在表格区域内

        Returns:
            int: 标题级别，0表示不是标题
        """
        # 如果文本为空，不是标题
        if not text.strip():
            return 0

        # 如果在表格区域内，不是标题（避免表格表头被误识别为标题）
        if is_in_table:
            return 0

        # 如果文本过长，不太可能是标题
        if len(text) > 300:  # 放宽长度限制
            return 0

        # 检查是否包含表格特征（如果包含，不太可能是标题）
        has_table_chars = '|' in text or '\t' in text
        if has_table_chars:
            return 0

        # 检查是否为常见的表格表头词汇
        table_header_words = ['参数名', '类型', '是否必须', '说明', '备注', '字段名', '字段类型', '必填', '描述',
                             '须', '是', '否', '必', '明', '注', '请求地址', '请求方式', '数据类型', '性能要求']
        if text.strip() in table_header_words:
            return 0

        # 检查是否为表格表头的组合词汇（如"是否必"）
        table_header_patterns = [
            r'^是否必$', r'^必须$', r'^是否$', r'^参数$', r'^字段$', r'^类型$', r'^说明$', r'^备注$',
            r'^请求$', r'^响应$', r'^地址$', r'^方式$', r'^数据$'
        ]
        for pattern in table_header_patterns:
            if re.match(pattern, text.strip()):
                return 0

        # 检查是否包含标题编号模式
        has_heading_number = bool(
            re.match(r'^(\d+\.)+\d*\s+\S', text) or
            re.match(r'^第[一二三四五六七八九十百千万亿]+[章节条款项]', text) or
            re.match(r'^[一二三四五六七八九十]、', text) or
            re.match(r'^[IVXLCDMivxlcdm]+\.\s+\S', text) or
            re.match(r'^\d+\s+[^\d\s]', text) or  # 简单数字编号
            re.match(r'^\d+\.\d+\s+\S', text) or  # 1.1 格式
            re.match(r'^\d+\.\d+\.\d+\s+\S', text)  # 1.1.1 格式
        )

        # 检查字体大小是否明显大于普通文本
        is_large_font = font_size > 12  # 降低字体大小阈值

        # 检查是否为短文本（标题通常较短）
        is_short_text = len(text) < 150 and text.count('\n') < 3  # 放宽短文本限制

        # 检查字体是否加粗
        is_bold = False
        if font_flags:
            # PyMuPDF中，flags & 16 表示加粗
            is_bold = any(flag & 16 for flag in font_flags)

        # 检查是否包含常见的标题关键词
        title_keywords = ['接口', '说明', '参数', '请求', '响应', '示例', '场景', '规范', '格式', '方法', '流程', '步骤']
        has_title_keywords = any(keyword in text for keyword in title_keywords)

        # 放宽标题检测条件

        # 1. 如果有明确的标题编号，很可能是标题
        if has_heading_number:
            if is_short_text:
                if is_large_font or is_bold:
                    return 1  # 一级标题
                else:
                    return 2  # 二级标题
            else:
                return 3  # 三级标题

        # 2. 如果字体较大或加粗，且文本较短，可能是标题
        if (is_large_font or is_bold) and is_short_text:
            # 如果靠近表格，需要更严格的条件
            if is_near_table:
                if has_title_keywords or (is_large_font and is_bold):
                    return 3  # 三级标题
            else:
                return 3  # 三级标题

        # 3. 如果包含标题关键词且文本较短，可能是标题
        if has_title_keywords and is_short_text and not is_near_table:
            return 3  # 三级标题

        return 0  # 不是标题

    def _extract_heading_number(self, text: str) -> Tuple[str, str]:
        """
        从标题文本中提取编号

        Args:
            text: 标题文本

        Returns:
            Tuple[str, str]: (编号, 清理后的文本)
        """
        # 匹配数字编号格式（如 "1.2.3 标题内容"）
        number_match = re.match(r'^(\d+\.)+\d*\s+(.+)$', text)
        if number_match:
            return number_match.group(1), number_match.group(2).strip()

        # 匹配中文编号格式（如 "第一章 标题内容"）
        cn_match = re.match(r'^(第[一二三四五六七八九十百千万亿]+[章节条款项]\s*)(.+)$', text)
        if cn_match:
            return cn_match.group(1), cn_match.group(2).strip()

        # 匹配罗马数字编号格式（如 "I. 标题内容"）
        roman_match = re.match(r'^([IVXLCDMivxlcdm]+\.\s*)(.+)$', text)
        if roman_match:
            return roman_match.group(1), roman_match.group(2).strip()

        # 匹配中文顺序编号（如 "一、标题内容"）
        cn_order_match = re.match(r'^([一二三四五六七八九十]、\s*)(.+)$', text)
        if cn_order_match:
            return cn_order_match.group(1), cn_order_match.group(2).strip()

        return "", text

    def _is_toc_heading(self, heading: str, original_text: str) -> bool:
        """
        检查标题是否为目录项

        Args:
            heading: 标题文本
            original_text: 原始文本

        Returns:
            bool: 是否为目录项
        """
        # 检查是否包含目录相关关键词
        toc_keywords = ['目录', '索引', 'contents', 'index', 'table of contents']
        heading_lower = heading.lower()

        for keyword in toc_keywords:
            if keyword in heading_lower:
                return True

        return False

    def _format_table_as_text(self, table_data: List[List[str]]) -> str:
        """
        将表格数据格式化为文本

        Args:
            table_data: 表格数据

        Returns:
            str: 格式化后的文本
        """
        if not table_data:
            return ""

        result = []

        for row in table_data:
            row_text = " | ".join([str(cell) for cell in row])
            result.append(row_text)

        return "\n".join(result)

    def _format_table_as_markdown(self, table_data: List[List[str]]) -> str:
        """
        将表格数据格式化为Markdown表格，智能识别表头

        Args:
            table_data: 表格数据

        Returns:
            str: Markdown表格
        """
        if not table_data or not table_data[0]:
            return ""

        result = []

        # 智能检测是否有真正的表头
        has_real_header = self._detect_table_header(table_data)

        if has_real_header:
            # 有真正的表头，使用第一行作为表头
            header = " | ".join([str(cell) for cell in table_data[0]])
            result.append("| " + header + " |")

            # 添加分隔行
            separator = " | ".join(["---" for _ in range(len(table_data[0]))])
            result.append("| " + separator + " |")

            # 添加数据行
            data_rows = table_data[1:]
        else:
            # 没有真正的表头，根据数据模式生成智能表头
            num_cols = len(table_data[0])
            smart_header = self._generate_smart_header(table_data)
            header = " | ".join(smart_header)
            result.append("| " + header + " |")

            # 添加分隔行
            separator = " | ".join(["---" for _ in range(num_cols)])
            result.append("| " + separator + " |")

            # 所有行都是数据行
            data_rows = table_data

        # 添加数据行
        for row in data_rows:
            # 确保行中的单元格数量与表头一致
            while len(row) < len(table_data[0]):
                row.append("")

            row_text = " | ".join([str(cell) for cell in row])
            result.append("| " + row_text + " |")

        return "\n".join(result)

    def _detect_table_header(self, table_data: List[List[str]]) -> bool:
        """
        检测表格是否有真正的表头

        Args:
            table_data: 表格数据

        Returns:
            bool: 是否有真正的表头
        """
        if not table_data or len(table_data) < 2:
            return False

        first_row = table_data[0]

        # 检查第一行是否包含典型的表头词汇
        header_keywords = [
            '参数名', '字段名', '名称', '类型', '说明', '描述', '备注', '示例',
            '是否必须', '必填', '可选', '默认值', '取值', '格式', '长度',
            'name', 'type', 'description', 'required', 'example', 'format'
        ]

        header_score = 0
        for cell in first_row:
            cell_str = str(cell).strip().lower()
            for keyword in header_keywords:
                if keyword.lower() in cell_str:
                    header_score += 1
                    break

        # 如果第一行有超过一半的单元格包含表头关键词，认为是表头
        if header_score >= len(first_row) * 0.5:
            return True

        # 检查第一行是否与其他行的数据模式不同
        if len(table_data) >= 3:
            # 检查第一行是否都是短文本（可能是表头）
            first_row_short = all(len(str(cell).strip()) < 20 for cell in first_row)

            # 检查其他行是否有更长的数据
            other_rows_longer = any(
                any(len(str(cell).strip()) > 20 for cell in row)
                for row in table_data[1:3]
            )

            if first_row_short and other_rows_longer:
                return True

        return False

    def _generate_smart_header(self, table_data: List[List[str]]) -> List[str]:
        """
        根据表格数据模式生成智能表头

        Args:
            table_data: 表格数据

        Returns:
            List[str]: 智能生成的表头
        """
        if not table_data or not table_data[0]:
            return ["列1", "列2", "列3"]

        num_cols = len(table_data[0])
        headers = []

        for col_idx in range(num_cols):
            # 获取该列的前几个值进行分析
            col_values = [row[col_idx] for row in table_data[:5] if col_idx < len(row)]

            if not col_values:
                headers.append(f"列{col_idx + 1}")
                continue

            # 分析数据模式
            header_name = self._analyze_column_pattern(col_values, col_idx)
            headers.append(header_name)

        return headers

    def _analyze_column_pattern(self, col_values: List[str], col_idx: int) -> str:
        """
        分析列数据模式，推断列名

        Args:
            col_values: 列数据值
            col_idx: 列索引

        Returns:
            str: 推断的列名
        """
        if not col_values:
            return f"列{col_idx + 1}"

        # 检查是否为错误码/状态码模式
        if all('_' in str(val) and any(c.isdigit() for c in str(val)) for val in col_values):
            return "状态码"

        # 检查是否包含"结果"、"状态"等关键词
        if any("结果" in str(val) for val in col_values):
            return "业务场景"

        # 检查是否包含"卡"、"银行"等关键词
        if any(any(keyword in str(val) for keyword in ["卡", "银行", "支付"]) for val in col_values):
            return "业务类型"

        # 检查是否为长文本描述（放在后面，优先级较低）
        if all(len(str(val).strip()) > 8 for val in col_values):
            return "说明"

        # 根据列位置和数据特征推断
        if col_idx == 0:
            # 第一列通常是分类或类型
            return "类型"
        elif col_idx == 1:
            # 第二列如果是短字符串，可能是代码
            if all(len(str(val).strip()) < 20 for val in col_values):
                return "代码"
            else:
                return "名称"
        elif col_idx == 2:
            # 第三列通常是描述
            return "描述"
        else:
            return f"列{col_idx + 1}"

    def get_original_content(self) -> str:
        """
        获取PDF文档的原始内容

        Returns:
            str: 原始内容字符串
        """
        content_blocks = self._parse_document()
        content_parts = []

        for block in content_blocks:
            if block['type'] in ['paragraph', 'heading']:
                content_parts.append(block['text'])
            elif block['type'] == 'table':
                # 将表格转换为文本格式
                table_text = self._format_table_as_text(block['data'])
                content_parts.append(f"[表格]\n{table_text}")
            elif block['type'] == 'image':
                content_parts.append(f"[图片: {block['width']:.0f}x{block['height']:.0f}]")

        return '\n\n'.join(content_parts)

    def get_markdown_content(self) -> str:
        """
        获取PDF文档的Markdown格式内容

        Returns:
            str: Markdown格式内容字符串
        """
        content_blocks = self._parse_document()
        content_parts = []

        for block in content_blocks:
            if block['type'] == 'heading':
                # 根据标题级别添加Markdown标题标记
                level = block.get('level', 1)
                heading_prefix = '#' * level

                # 构建完整的标题文本（包含原始编号）
                original_number = block['original_number']
                clean_text = block['clean_text']

                if original_number:
                    heading_text = f"{original_number} {clean_text}"
                else:
                    heading_text = clean_text

                content_parts.append(f"{heading_prefix} {heading_text}")

            elif block['type'] == 'paragraph':
                content_parts.append(block['text'])

            elif block['type'] == 'table':
                # 将表格转换为Markdown表格格式
                table_markdown = self._format_table_as_markdown(block['data'])
                content_parts.append(table_markdown)

            elif block['type'] == 'image':
                # 添加图片的Markdown格式
                content_parts.append(f"![图片]({block['width']:.0f}x{block['height']:.0f})")

        return '\n\n'.join(content_parts)

    def get_all_headings(self, exclude_toc: bool = True) -> List[str]:
        """
        获取文档中的所有标题，可选择排除目录信息

        Args:
            exclude_toc: 是否排除目录中的信息，默认为True

        Returns:
            List[str]: 标题字符串列表，保留原始编号
        """
        if self._headings is not None and exclude_toc:
            return self._headings

        content_blocks = self._parse_document()
        headings = []

        for block in content_blocks:
            if block['type'] == 'heading':
                # 构建完整的标题文本（包含原始编号）
                original_number = block['original_number']
                clean_text = block['clean_text']

                if original_number:
                    heading_text = f"{original_number} {clean_text}"
                else:
                    heading_text = clean_text

                # 如果需要排除目录，则过滤目录相关的标题
                if exclude_toc and self._is_toc_heading(heading_text, block['text']):
                    continue

                headings.append(heading_text)

        # 去重并保持顺序
        final_headings = []
        seen = set()
        for heading in headings:
            if heading not in seen:
                final_headings.append(heading)
                seen.add(heading)

        if exclude_toc:
            self._headings = final_headings
        return final_headings

    def _sort_headings_by_document_order(self, selected_headings: List[str], all_headings: List[str]) -> List[str]:
        """
        按照文档中的顺序对选定的标题进行排序

        Args:
            selected_headings: 选定的标题列表
            all_headings: 所有标题列表（按文档顺序）

        Returns:
            List[str]: 按文档顺序排序的选定标题列表
        """
        # 创建标题到索引的映射
        heading_index_map = {heading: idx for idx, heading in enumerate(all_headings)}

        # 过滤出在文档中存在的标题，并按文档顺序排序
        valid_headings = [h for h in selected_headings if h in heading_index_map]
        return sorted(valid_headings, key=lambda h: heading_index_map[h])

    def split_markdown_by_headings(self) -> List[Dict[str, Any]]:
        """
        使用get_all_headings中的标题，将get_markdown_content的内容按标题分割成文档块

        Returns:
            List[Dict[str, Any]]: 按标题分割的文档块列表
        """
        # 获取所有标题和markdown内容
        headings = self.get_all_headings()
        markdown_content = self.get_markdown_content()

        if not headings or not markdown_content:
            return []

        # 分割markdown内容为行
        lines = markdown_content.split('\n')
        document_blocks = []

        # 创建标题到行号的映射
        heading_line_map = {}

        # 遍历所有行，找到标题行
        for line_num, line in enumerate(lines):
            line_stripped = line.strip()

            # 检查是否是markdown标题行
            if line_stripped.startswith('#'):
                # 提取标题文本（去掉#号和空格）
                title_match = re.match(r'^(#{1,6})\s+(.+)$', line_stripped)
                if title_match:
                    level = len(title_match.group(1))
                    title_text = title_match.group(2).strip()

                    # 检查这个标题是否在我们的标题列表中
                    for heading in headings:
                        if heading == title_text or heading.endswith(title_text):
                            heading_line_map[heading] = {
                                'line_num': line_num,
                                'level': level,
                                'raw_line': line_stripped
                            }
                            break

        # 按行号排序标题
        sorted_headings = sorted(heading_line_map.items(), key=lambda x: x[1]['line_num'])

        # 为每个标题创建文档块
        for i, (heading, info) in enumerate(sorted_headings):
            start_line = info['line_num']

            # 确定结束行：找到下一个标题行
            end_line = len(lines) - 1  # 默认到文档末尾
            if i + 1 < len(sorted_headings):
                end_line = sorted_headings[i + 1][1]['line_num'] - 1

            # 提取内容（不包括标题行本身）
            content_lines = lines[start_line + 1:end_line + 1]
            content = '\n'.join(content_lines)

            # 创建文档块
            block = {
                'heading': heading,
                'start_line': start_line,
                'end_line': end_line,
                'content': content,
                'line_count': len(content_lines),
                'char_count': len(content),
                'level': info['level'],
                'raw_title_line': info['raw_line']
            }

            # 分析内容特征
            block['has_table'] = '|' in content and '---' in content
            block['has_code'] = '```' in content or (content.count('`') >= 2)
            block['has_image'] = '![' in content

            # 计算段落数（非空行数）
            non_empty_lines = [line for line in content_lines if line.strip()]
            block['paragraph_count'] = len(non_empty_lines)

            document_blocks.append(block)

        return document_blocks

    def split_markdown_by_selected_headings(self, selected_headings: List[str]) -> List[Dict[str, Any]]:
        """
        根据提供的部分标题列表，将get_markdown_content的内容按指定标题进行文档切割

        Args:
            selected_headings: 用户选择的标题列表（必须是get_all_headings()结果的子集）

        Returns:
            List[Dict[str, Any]]: 按选定标题分割的文档块列表

        Raises:
            ValueError: 如果提供的标题不在文档的标题列表中
        """
        if not selected_headings:
            return []

        # 获取所有标题和markdown内容
        all_headings = self.get_all_headings()
        markdown_content = self.get_markdown_content()

        if not all_headings or not markdown_content:
            return []

        # 验证选定的标题是否都在文档中
        invalid_headings = []
        for heading in selected_headings:
            if heading not in all_headings:
                invalid_headings.append(heading)

        if invalid_headings:
            raise ValueError(f"以下标题不在文档中: {invalid_headings}")

        # 分割markdown内容为行
        lines = markdown_content.split('\n')

        # 创建所有标题到行号的映射
        all_heading_line_map = {}
        selected_heading_line_map = {}

        # 遍历所有行，找到标题行
        for line_num, line in enumerate(lines):
            line_stripped = line.strip()

            # 检查是否是markdown标题行
            if line_stripped.startswith('#'):
                title_match = re.match(r'^(#{1,6})\s+(.+)$', line_stripped)
                if title_match:
                    level = len(title_match.group(1))
                    title_text = title_match.group(2).strip()

                    # 检查这个标题是否在所有标题列表中
                    for heading in all_headings:
                        if heading == title_text or heading.endswith(title_text):
                            all_heading_line_map[heading] = {
                                'line_num': line_num,
                                'level': level,
                                'raw_line': line_stripped
                            }

                            # 如果这个标题在选定列表中，也记录下来
                            if heading in selected_headings:
                                selected_heading_line_map[heading] = {
                                    'line_num': line_num,
                                    'level': level,
                                    'raw_line': line_stripped
                                }
                            break

        # 获取所有标题的行号列表（用于确定边界）
        all_heading_lines = sorted([info['line_num'] for info in all_heading_line_map.values()])

        # 按行号排序选定的标题
        sorted_selected_headings = sorted(selected_heading_line_map.items(), key=lambda x: x[1]['line_num'])

        document_blocks = []

        for i, (heading, info) in enumerate(sorted_selected_headings):
            start_line = info['line_num']

            # 确定结束行：找到下一个标题行（任何级别的标题）
            end_line = len(lines) - 1  # 默认到文档末尾

            for heading_line in all_heading_lines:
                if heading_line > start_line:
                    end_line = heading_line - 1
                    break

            # 提取内容
            content_lines = lines[start_line:end_line + 1]
            content = '\n'.join(content_lines)

            # 创建文档块
            block = {
                'heading': heading,
                'start_line': start_line,
                'end_line': end_line,
                'content': content,
                'line_count': len(content_lines),
                'char_count': len(content),
                'level': info['level'],
                'raw_title_line': info['raw_line'],
                'selected_index': selected_headings.index(heading)  # 在选定列表中的索引
            }

            # 分析内容特征
            block['has_table'] = '|' in content and '---' in content
            block['has_code'] = '```' in content or (content.count('`') >= 2)
            block['has_image'] = '![' in content

            # 计算段落数（非空行数，排除标题行）
            non_empty_lines = [line for line in content_lines[1:] if line.strip()]
            block['paragraph_count'] = len(non_empty_lines)

            document_blocks.append(block)

        return document_blocks

    def split_headings_by_selected_headings(self, selected_headings: List[str]) -> List[List[Dict[str, Any]]]:
        """
        根据给定的标题列表，先按照get_all_headings的标题顺序进行排序，然后进行切分，
        返回的是列表里面还是列表，里面的列表是分割后的所有标题的document_block

        Args:
            selected_headings: 用户选择的标题列表（必须是get_all_headings()结果的子集）

        Returns:
            List[List[Dict[str, Any]]]: 按选定标题切分后的标题块列表，每个块包含从当前选定标题到下一个选定标题之间的所有标题的document_blocks

        Raises:
            ValueError: 如果提供的标题不在文档的标题列表中
        """
        if not selected_headings:
            return []

        # 获取所有标题和markdown内容
        all_headings = self.get_all_headings()
        markdown_content = self.get_markdown_content()

        if not all_headings or not markdown_content:
            return []

        # 验证选定的标题是否都在文档中
        invalid_headings = []
        for heading in selected_headings:
            if heading not in all_headings:
                invalid_headings.append(heading)

        if invalid_headings:
            raise ValueError(f"以下标题不在文档中: {invalid_headings}")

        # 按照get_all_headings中的顺序对选定标题进行排序
        sorted_selected_headings = self._sort_headings_by_document_order(selected_headings, all_headings)

        # 获取所有文档块
        all_document_blocks = self.split_markdown_by_headings()

        # 创建标题到文档块的映射
        heading_to_block = {block['heading']: block for block in all_document_blocks}

        # 创建结果列表
        result = []

        # 遍历排序后的选定标题
        for i, current_heading in enumerate(sorted_selected_headings):
            # 确定下一个选定标题的索引
            next_heading_idx = None
            if i + 1 < len(sorted_selected_headings):
                next_heading = sorted_selected_headings[i + 1]
                next_heading_idx = all_headings.index(next_heading)

            # 获取当前选定标题在所有标题中的索引
            current_idx = all_headings.index(current_heading)

            # 获取从当前选定标题到下一个选定标题之间的所有标题
            section_headings = []
            if next_heading_idx is not None:
                section_headings = all_headings[current_idx:next_heading_idx]
            else:
                section_headings = all_headings[current_idx:]

            # 获取这些标题对应的文档块
            section_blocks = []
            for heading in section_headings:
                if heading in heading_to_block:
                    section_blocks.append(heading_to_block[heading])

            result.append(section_blocks)

        return result

    def get_headings_document_blocks_dict(self, selected_headings: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """
        根据给定的标题列表，先按照get_all_headings的标题顺序进行排序，然后进行切分，
        返回一个dict，key值为标题名称，value值为document_blocks列表

        Args:
            selected_headings: 用户选择的标题列表（必须是get_all_headings()结果的子集）

        Returns:
            Dict[str, List[Dict[str, Any]]]: 字典，key为选定标题名称，value为该标题对应的document_blocks列表

        Raises:
            ValueError: 如果提供的标题不在文档的标题列表中
        """
        if not selected_headings:
            return {}

        # 调用split_headings_by_selected_headings方法获取嵌套列表结构
        nested_blocks = self.split_headings_by_selected_headings(selected_headings)

        # 按照get_all_headings中的顺序对选定标题进行排序
        all_headings = self.get_all_headings()
        sorted_selected_headings = self._sort_headings_by_document_order(selected_headings, all_headings)

        # 构建字典结果
        result_dict = {}

        for i, heading in enumerate(sorted_selected_headings):
            if i < len(nested_blocks):
                result_dict[heading] = nested_blocks[i]
            else:
                result_dict[heading] = []  # 如果没有对应的块，返回空列表

        return result_dict

    def get_document_info(self) -> Dict[str, Any]:
        """
        获取文档的基本信息

        Returns:
            Dict[str, Any]: 文档信息字典
        """
        doc = self._load_document()
        content_blocks = self._parse_document()

        # 统计各类型内容块数量
        paragraph_count = sum(1 for block in content_blocks if block['type'] == 'paragraph')
        heading_count = sum(1 for block in content_blocks if block['type'] == 'heading')
        table_count = sum(1 for block in content_blocks if block['type'] == 'table')
        image_count = sum(1 for block in content_blocks if block['type'] == 'image')

        # 获取文档元数据
        metadata = doc.metadata

        return {
            'file_path': self.original_path,
            'title': metadata.get('title', Path(self.original_path).stem),
            'author': metadata.get('author', ''),
            'created': metadata.get('creationDate', ''),
            'modified': metadata.get('modDate', ''),
            'page_count': len(doc),
            'paragraph_count': paragraph_count,
            'heading_count': heading_count,
            'table_count': table_count,
            'image_count': image_count,
            'total_blocks': len(content_blocks)
        }

    def parse_document(self, output_format: str = 'original') -> str:
        """
        解析文档并返回指定格式的内容

        Args:
            output_format: 输出格式 ('original', 'markdown', 'headings')

        Returns:
            str: 解析后的内容
        """
        if output_format == 'original':
            return self.get_original_content()
        elif output_format == 'markdown':
            return self.get_markdown_content()
        elif output_format == 'headings':
            headings = self.get_all_headings()
            return '\n'.join(headings)
        else:
            raise ValueError(f"不支持的输出格式: {output_format}")
