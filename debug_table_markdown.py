"""
调试表格Markdown格式化问题
"""

from pdf_document_parser import PdfDocumentParser

def debug_table_markdown():
    """调试表格Markdown格式化问题"""
    
    pdf_file_path = "D:\\工作文档\\20210826头条放心借\\智选平台_基线文档_v0.0.1.pdf"
    
    print("调试表格Markdown格式化问题")
    print("=" * 60)
    
    try:
        parser = PdfDocumentParser(pdf_file_path)
        
        print("1. 检查原始表格数据:")
        
        # 获取所有表格块
        content_blocks = parser._parse_document()
        table_blocks = [block for block in content_blocks if block['type'] == 'table']
        
        print(f"   找到 {len(table_blocks)} 个表格")
        
        for i, table_block in enumerate(table_blocks):
            print(f"\n   表格 {i+1}:")
            table_data = table_block.get('data', [])
            print(f"     行数: {len(table_data)}")
            print(f"     列数: {len(table_data[0]) if table_data else 0}")
            
            # 显示原始数据的前几行
            print("     原始数据前5行:")
            for j, row in enumerate(table_data[:5]):
                print(f"       行{j+1}: {row}")
            
            # 测试当前的格式化方法
            print("     当前Markdown格式化:")
            formatted = parser._format_table_as_markdown(table_data)
            lines = formatted.split('\n')
            for j, line in enumerate(lines[:8]):
                print(f"       {j+1}: {line}")
            if len(lines) > 8:
                print(f"       ... (还有 {len(lines) - 8} 行)")
        
        print("\n2. 检查Markdown内容中的表格:")
        
        markdown_content = parser.get_markdown_content()
        lines = markdown_content.split('\n')
        
        # 找到表格相关的行
        table_start_indices = []
        for i, line in enumerate(lines):
            if '|' in line and line.count('|') >= 3:
                # 检查是否是表格开始（前面有标题或空行）
                if i == 0 or not lines[i-1].strip() or lines[i-1].startswith('#'):
                    table_start_indices.append(i)
        
        print(f"   在Markdown中找到 {len(table_start_indices)} 个可能的表格开始位置")
        
        for i, start_idx in enumerate(table_start_indices[:3]):  # 只显示前3个
            print(f"\n   表格 {i+1} (从行 {start_idx+1} 开始):")
            
            # 显示表格前后的上下文
            context_start = max(0, start_idx - 2)
            context_end = min(len(lines), start_idx + 10)
            
            for j in range(context_start, context_end):
                prefix = ">>>" if j == start_idx else "   "
                print(f"     {prefix} 行{j+1}: {lines[j]}")
        
        print("\n3. 分析问题:")
        
        # 检查表格格式化的具体问题
        if table_blocks:
            test_table = table_blocks[0]
            test_data = test_table.get('data', [])
            
            print("   分析第一个表格的格式化问题:")
            print(f"     原始数据结构: {len(test_data)} 行 x {len(test_data[0]) if test_data else 0} 列")
            
            if test_data:
                # 检查是否有表头
                first_row = test_data[0]
                print(f"     第一行内容: {first_row}")
                
                # 检查数据的一致性
                col_counts = [len(row) for row in test_data]
                print(f"     各行列数: {col_counts[:10]}...")  # 显示前10行的列数
                
                if len(set(col_counts)) > 1:
                    print("     ⚠️ 发现行列数不一致的问题")
                else:
                    print("     ✅ 所有行的列数一致")
                
                # 检查空单元格
                empty_cells = 0
                total_cells = 0
                for row in test_data:
                    for cell in row:
                        total_cells += 1
                        if not cell.strip():
                            empty_cells += 1
                
                print(f"     空单元格比例: {empty_cells}/{total_cells} ({empty_cells/total_cells*100:.1f}%)")
        
        print("\n4. 期望的表格格式:")
        print("   标准Markdown表格应该是:")
        print("   | 列1 | 列2 | 列3 |")
        print("   | --- | --- | --- |")
        print("   | 数据1 | 数据2 | 数据3 |")
        print("   | 数据4 | 数据5 | 数据6 |")
        
        print("\n5. 可能的问题:")
        print("   - 表格数据提取不完整")
        print("   - 表头识别有问题")
        print("   - 格式化方法需要改进")
        print("   - 需要更好的表格边界检测")
        
    except Exception as e:
        print(f"调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_table_markdown()
